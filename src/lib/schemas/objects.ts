/**
 * Zod schemas corresponding to existing TypeScript interfaces in objects.tsx
 * These schemas provide runtime validation for the existing type definitions
 */

import { z } from "zod";
import {
  NonEmptyStringSchema,
  PositiveIntegerSchema,
  TraitCategorySchema,
  CoordinatesSchema,
  UuidSchema,
  YearSchema,
} from "./common";

// Base attribute schema
export const AttributeSchema = z.object({
  attribute: NonEmptyStringSchema,
  active: z.boolean(),
  levels: z.array(z.string()),
  category: TraitCategorySchema.optional(),
  attribute_type: z.string(),
});

// File state schema
export const FileStateSchema = z.object({
  file: z.instanceof(File).nullable(),
  data: z.array(z.unknown()).nullable(),
  error: z.string().nullable(),
});

// Attribute response schema
export const AttributeResponseSchema = z.object({
  attribute: NonEmptyStringSchema,
  levels: z.array(z.string()),
  category: TraitCategorySchema.optional(),
  attribute_type: z.string(),
});

// Attribute level schema
export const AttributeLevelSchema = z.object({
  attribute: NonEmptyStringSchema,
  levels: z.array(z.string()),
  category: TraitCategorySchema.optional(),
});

// Brand attribute combination schema
export const BrandAttributeCombinationSchema = z.record(
  z.string(), // brand name
  z.record(z.string(), z.string()) // attribute -> value mapping
);

// Real world attributes response schema
export const RealWorldAttributesResponseSchema = z.object({
  attributes: z.array(AttributeResponseSchema),
  success: z.boolean(),
  message: z.string().optional(),
});

// Orthogonal attributes levels request schema
export const OrthogonalAttributesLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  attribute_count: PositiveIntegerSchema,
  year: YearSchema.optional(),
  category: TraitCategorySchema.optional(),
});

// Add new attribute card levels object schema
export const AddNewAttributeCardLevelsObjectSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  new_attribute: NonEmptyStringSchema,
  existing_attributes: z.array(z.string()),
  levels_count: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// Add new trait labels object schema
export const AddNewTraitLabelsObjectSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  new_trait: NonEmptyStringSchema,
  existing_traits: z.array(z.string()),
  levels_count: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// State schema (geographic)
export const StateSchema = z.object({
  name: NonEmptyStringSchema,
  code: z.string().length(2), // State codes are 2 characters
  flag: NonEmptyStringSchema,
  coords: CoordinatesSchema,
});

// Country schema (geographic)
export const CountrySchema = z.object({
  name: NonEmptyStringSchema,
  flag: NonEmptyStringSchema,
  coords: CoordinatesSchema,
  states: z.array(StateSchema).optional(),
});

// Level schema for display attributes
export const LevelSchema = z.object({
  level: NonEmptyStringSchema,
  active: z.boolean(),
});

// Display attribute schema (enhanced)
export const DisplayAttributeSchema = z.object({
  attribute: NonEmptyStringSchema,
  active: z.boolean(),
  levels: z.array(LevelSchema),
  category: TraitCategorySchema.optional(),
  attribute_type: z.enum(["categorical", "numerical", "ordinal"]),
});

// Display trait schema
export const DisplayTraitSchema = z.object({
  title: NonEmptyStringSchema,
  active: z.boolean(),
  values: z.array(z.string()),
  category: TraitCategorySchema.optional(),
});

// Population traits schema (comprehensive)
export const PopulationTraitsSchema = z.object({
  // US Specific Traits
  state: z.string().nullable(),
  census_division: z.string().optional(),
  marital_status: z.string().optional(),
  family_size: z.string().optional(),
  hispanic_latino: z.boolean().optional(),
  veteran_status: z.string().optional(),
  migration_status: z.string().optional(),
  speaks_english: z.string().optional(),
  health_insurance: z.boolean().optional(),
  vehicles_in_household: z.string().optional(),

  // Non-US Demographics
  gender: z.array(z.string()).min(1),
  age: z.tuple([z.number().int().min(0), z.number().int().min(0)]),
  education_level: z.array(z.string()).min(1),
  racial_group: z.array(z.string()).min(1),
  hispanic_latino_origin: z.string().optional(),
  home_ownership: z.string().optional(),
  household_size: z.string().optional(),
  household_vehicles: z.string().optional(),
  household_with_children: z.boolean().optional(),
  household_income: z.tuple([z.number().min(0), z.number().min(0)]),
  number_of_children: z.array(z.string()).min(1),

  // Lifestyle Traits
  lifestyle_traits: z
    .object({
      health_consciousness: z.string().optional(),
      price_sensitivity: z.string().optional(),
      brand_loyalty: z.string().optional(),
      environmental_concern: z.string().optional(),
      innovation_adoption: z.string().optional(),
      quality_orientation: z.string().optional(),
      convenience_priority: z.string().optional(),
    })
    .optional(),

  // Psychological Traits
  psychological_traits: z
    .object({
      need_for_uniqueness: z.string().optional(),
      self_monitoring: z.string().optional(),
      risk_aversion: z.string().optional(),
      impulsiveness: z.string().optional(),
      need_for_cognition: z.string().optional(),
      time_orientation: z.string().optional(),
      self_construal: z.string().optional(),
      materialism: z.string().optional(),
    })
    .optional(),
});

// Create experiment request schema
export const CreateExperimentRequestSchema = z.object({
  question: NonEmptyStringSchema,
  population_traits: z.array(DisplayTraitSchema),
  populationTraits: PopulationTraitsSchema.optional(),
  displayAttributes: z.array(DisplayAttributeSchema),
  realworld_products: z.array(BrandAttributeCombinationSchema),
  year: YearSchema,
  country: NonEmptyStringSchema,
  is_private: z.boolean().optional().default(false),
  state: z.string().nullable().optional(),
  external_personas: z
    .array(
      z.object({
        id: UuidSchema,
        name: NonEmptyStringSchema,
        // Add other persona fields as needed
      })
    )
    .optional(),
  experiment_type: z.enum(["conjoint", "concept_testing"]),
  concept_description: z.string().optional(),
  concept_statements: z.array(z.unknown()).optional(),
  image_name: z.string().optional(),
});

// Attributes levels request schema
export const AttributesLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  attribute_count: PositiveIntegerSchema,
  year: YearSchema.optional(),
  category: TraitCategorySchema.optional(),
});

// Create levels request schema
export const CreateLevelsRequestSchema = z.object({
  why_prompt: NonEmptyStringSchema,
  country: z.string().optional(),
  level_count: PositiveIntegerSchema,
  values: z.array(z.string()).min(1),
  category: TraitCategorySchema.optional(),
});

// Real world product check request schema
export const RealWorldProductCheckRequestSchema = z.object({
  question: NonEmptyStringSchema,
});

// Real world product attribute request schema
export const RealWorldProductAttributeRequestSchema = z.object({
  number_of_attributes: PositiveIntegerSchema,
  country: NonEmptyStringSchema,
  why_prompt: NonEmptyStringSchema,
  category: TraitCategorySchema.optional(),
});

// Trait schema
export const TraitSchema = z.object({
  id: UuidSchema,
  set_type: z.string().nullable(),
  type: NonEmptyStringSchema,
  short_description: NonEmptyStringSchema,
  long_description: NonEmptyStringSchema,
  measurement_type: z.enum(["categorical", "ordinal", "interval", "ratio"]),
  ordinal_rank: z.number().nullable(),
  category: TraitCategorySchema.optional(),
});

// LLM model schema
export const LLMModelSchema = z.object({
  name: z.enum([
    "gpt-4",
    "gpt-4-turbo",
    "gpt-3.5-turbo",
    "claude-3-sonnet",
    "claude-3-haiku",
  ]),
});

// State data schema for demographic forms
export const StateDataSchema = z.object({
  Age: z.object({
    min: z.number(),
    max: z.number(),
    "90th_percentile": z.number(),
    "95th_percentile": z.number(),
    userMin: z.number().optional(),
    userMax: z.number().optional(),
  }),
  "Household income": z.object({
    min: z.number(),
    max: z.number(),
    "90th_percentile": z.number(),
    "95th_percentile": z.number(),
    userMin: z.number().optional(),
    userMax: z.number().optional(),
  }),
});

// Final selected population traits schema
export const FinalSelectedPopulationTraitsSchema = z.object({
  population_size: PositiveIntegerSchema,
  population_traits: z.record(
    z.union([
      z.string(),
      z.number(),
      z.array(z.union([z.string(), z.number()])),
    ])
  ),
});

// One trait change traits schema
export const OneTraitChangeTraitsSchema = z.record(
  z.string(),
  z.object({
    population_size: PositiveIntegerSchema,
    population_traits: z.record(z.unknown()),
  })
);

// Validation response schema
export const ValidationResponseSchema = z.object({
  original: FinalSelectedPopulationTraitsSchema,
  suggestion: FinalSelectedPopulationTraitsSchema,
  one_trait_change: OneTraitChangeTraitsSchema,
});

// Product levels request schema
export const ProductLevelsRequestSchema = z.object({
  attribute_count: PositiveIntegerSchema,
  level_count: PositiveIntegerSchema,
  country: z.string().optional(),
  why_prompt: NonEmptyStringSchema,
  num_levels: PositiveIntegerSchema,
  num_attrs: PositiveIntegerSchema,
  category: TraitCategorySchema.optional(),
});

// Persona schema
export const PersonaSchema = z.object({
  id: UuidSchema,
  name: NonEmptyStringSchema,
  age: z.string(),
  gender: z.string(),
  maritalStatus: z.string(),
  income: z.string(),
  education: z.string(),
  racialGroup: z.string(),
  homeOwnership: z.string(),
  vehiclesOwned: z.string(),
  hasDrivingLicense: z.string(),
  location: z.string(),
  occupation: z.string(),
  background: z.string(),
  goals: z.string(),
  painPoints: z.string(),
  personalityTraits: z.string(),
  behaviors: z.string(),
  isDescriptionBased: z.boolean().optional(),
  description: z.string().optional(),
  linkedInSource: z.string().optional(),
});

// Export types that match the original interfaces
export type Attribute = z.infer<typeof AttributeSchema>;
export type FileState = z.infer<typeof FileStateSchema>;
export type AttributeResponse = z.infer<typeof AttributeResponseSchema>;
export type AttributeLevel = z.infer<typeof AttributeLevelSchema>;
export type BrandAttributeCombination = z.infer<
  typeof BrandAttributeCombinationSchema
>;
export type RealWorldAttributesResponse = z.infer<
  typeof RealWorldAttributesResponseSchema
>;
export type OrthogonalAttributesLevelsRequest = z.infer<
  typeof OrthogonalAttributesLevelsRequestSchema
>;
export type AddNewAttributeCardLevelsObject = z.infer<
  typeof AddNewAttributeCardLevelsObjectSchema
>;
export type AddNewTraitLabelsObject = z.infer<
  typeof AddNewTraitLabelsObjectSchema
>;
export type State = z.infer<typeof StateSchema>;
export type Country = z.infer<typeof CountrySchema>;
export type Level = z.infer<typeof LevelSchema>;
export type DisplayAttribute = z.infer<typeof DisplayAttributeSchema>;
export type DisplayTrait = z.infer<typeof DisplayTraitSchema>;
export type PopulationTraits = z.infer<typeof PopulationTraitsSchema>;
export type CreateExperimentRequest = z.infer<
  typeof CreateExperimentRequestSchema
>;
export type AttributesLevelsRequest = z.infer<
  typeof AttributesLevelsRequestSchema
>;
export type CreateLevelsRequest = z.infer<typeof CreateLevelsRequestSchema>;
export type RealWorldProductCheckRequest = z.infer<
  typeof RealWorldProductCheckRequestSchema
>;
export type RealWorldProductAttributeRequest = z.infer<
  typeof RealWorldProductAttributeRequestSchema
>;
export type Trait = z.infer<typeof TraitSchema>;
export type LLMModel = z.infer<typeof LLMModelSchema>;
export type StateData = z.infer<typeof StateDataSchema>;
export type FinalSelectedPopulationTraits = z.infer<
  typeof FinalSelectedPopulationTraitsSchema
>;
export type OneTraitChangeTraits = z.infer<typeof OneTraitChangeTraitsSchema>;
export type ValidationResponse = z.infer<typeof ValidationResponseSchema>;
export type ProductLevelsRequest = z.infer<typeof ProductLevelsRequestSchema>;
export type Persona = z.infer<typeof PersonaSchema>;
