/**
 * Demographic and population trait validation schemas
 * These schemas validate demographic data, population traits, and related forms
 */

import { z } from "zod";
import {
  NonEmptyStringSchema,
  RangeSchema,
  TraitCategorySchema,
  NonEmptyArraySchema,
  PositiveIntegerSchema,
} from "./common";

// Age range schema
export const AgeRangeSchema = z
  .object({
    min: z.number().int().min(0).max(120),
    max: z.number().int().min(0).max(120),
  })
  .refine((data) => data.min <= data.max, {
    message: "Minimum age must be less than or equal to maximum age",
  });

// Income range schema
export const IncomeRangeSchema = z
  .object({
    min: z.number().min(0),
    max: z.number().min(0),
  })
  .refine((data) => data.min <= data.max, {
    message: "Minimum income must be less than or equal to maximum income",
  });

// Education level schema
export const EducationLevelSchema = z.enum([
  "Less than high school",
  "High school graduate",
  "Some college",
  "Associate degree",
  "Bachelor's degree",
  "Master's degree",
  "Professional degree",
  "Doctoral degree",
]);

// Gender schema
export const GenderSchema = z.enum([
  "Male",
  "Female",
  "Non-binary",
  "Other",
  "Prefer not to say",
]);

// Racial group schema
export const RacialGroupSchema = z.enum([
  "White",
  "Black or African American",
  "American Indian or Alaska Native",
  "Asian",
  "Native Hawaiian or Other Pacific Islander",
  "Two or more races",
  "Other",
  "Prefer not to say",
]);

// Number of children schema
export const NumberOfChildrenSchema = z.enum(["0", "1", "2", "3", "4", "5+"]);

// Marital status schema
export const MaritalStatusSchema = z.enum([
  "Single",
  "Married",
  "Divorced",
  "Widowed",
  "Separated",
  "Domestic partnership",
]);

// Home ownership schema
export const HomeOwnershipSchema = z.enum(["Own", "Rent", "Other"]);

// Household size schema
export const HouseholdSizeSchema = z.enum(["1", "2", "3", "4", "5", "6+"]);

// Lifestyle traits schema
export const LifestyleTraitsSchema = z.object({
  health_consciousness: z.enum(["Low", "Medium", "High"]).optional(),
  price_sensitivity: z.enum(["Low", "Medium", "High"]).optional(),
  brand_loyalty: z.enum(["Low", "Medium", "High"]).optional(),
  environmental_concern: z.enum(["Low", "Medium", "High"]).optional(),
  innovation_adoption: z
    .enum(["Early Adopter", "Early Majority", "Late Majority", "Laggard"])
    .optional(),
  quality_orientation: z.enum(["Low", "Medium", "High"]).optional(),
  convenience_priority: z.enum(["Low", "Medium", "High"]).optional(),
});

// Psychological traits schema
export const PsychologicalTraitsSchema = z.object({
  need_for_uniqueness: z.enum(["Low", "Medium", "High"]).optional(),
  self_monitoring: z.enum(["Low", "Medium", "High"]).optional(),
  risk_aversion: z.enum(["Low", "Medium", "High"]).optional(),
  impulsiveness: z.enum(["Low", "Medium", "High"]).optional(),
  need_for_cognition: z.enum(["Low", "Medium", "High"]).optional(),
  time_orientation: z.enum(["Past", "Present", "Future"]).optional(),
  self_construal: z.enum(["Independent", "Interdependent"]).optional(),
  materialism: z.enum(["Low", "Medium", "High"]).optional(),
});

// Population traits schema (main demographic data structure)
export const PopulationTraitsSchema = z.object({
  // Geographic
  state: z.string().nullable(),
  census_division: z.string().optional(),

  // Basic demographics
  gender: NonEmptyArraySchema(GenderSchema),
  age: z.tuple([z.number().int().min(0), z.number().int().min(0)]),
  education_level: NonEmptyArraySchema(EducationLevelSchema),
  racial_group: NonEmptyArraySchema(RacialGroupSchema),
  household_income: z.tuple([z.number().min(0), z.number().min(0)]),
  number_of_children: NonEmptyArraySchema(NumberOfChildrenSchema),

  // Optional US-specific traits
  marital_status: z.string().optional(),
  family_size: z.string().optional(),
  hispanic_latino: z.boolean().optional(),
  hispanic_latino_origin: z.string().optional(),
  veteran_status: z.string().optional(),
  migration_status: z.string().optional(),
  speaks_english: z.string().optional(),
  health_insurance: z.boolean().optional(),
  vehicles_in_household: z.string().optional(),
  home_ownership: HomeOwnershipSchema.optional(),
  household_size: HouseholdSizeSchema.optional(),
  household_vehicles: z.string().optional(),
  household_with_children: z.boolean().optional(),

  // Advanced traits
  lifestyle_traits: LifestyleTraitsSchema.optional(),
  psychological_traits: PsychologicalTraitsSchema.optional(),
});

// Validation request schema for population traits
export const PopulationTraitsValidationRequestSchema = z.object({
  age: z.tuple([z.number().int().min(0), z.number().int().min(0)]),
  education_level: NonEmptyArraySchema(z.string()),
  gender: NonEmptyArraySchema(z.string()),
  household_income: z.tuple([z.number().min(0), z.number().min(0)]),
  number_of_children: NonEmptyArraySchema(z.string()),
  number_of_records: PositiveIntegerSchema.default(300),
  racial_group: NonEmptyArraySchema(z.string()),
  state: z.string().nullable(),
});

// Final selected population traits schema
export const FinalSelectedPopulationTraitsSchema = z.object({
  population_size: PositiveIntegerSchema,
  population_traits: z.record(
    z.union([
      z.string(),
      z.number(),
      z.array(z.union([z.string(), z.number()])),
    ])
  ),
});

// One trait change traits schema
export const OneTraitChangeTraitsSchema = z.record(
  z.string(),
  z.object({
    population_size: PositiveIntegerSchema,
    population_traits: z.record(z.unknown()),
  })
);

// Validation response schema
export const ValidationResponseSchema = z.object({
  original: FinalSelectedPopulationTraitsSchema,
  suggestion: FinalSelectedPopulationTraitsSchema,
  one_trait_change: OneTraitChangeTraitsSchema,
});

// State data schema for demographic forms
export const StateDataSchema = z.object({
  Age: z.object({
    min: z.number(),
    max: z.number(),
    "90th_percentile": z.number(),
    "95th_percentile": z.number(),
    userMin: z.number().optional(),
    userMax: z.number().optional(),
  }),
  "Household income": z.object({
    min: z.number(),
    max: z.number(),
    "90th_percentile": z.number(),
    "95th_percentile": z.number(),
    userMin: z.number().optional(),
    userMax: z.number().optional(),
  }),
});

// Demographic traits form schema
export const DemographicTraitsFormSchema = z.object({
  ageRange: z.tuple([z.number(), z.number()]),
  incomeRange: z.tuple([z.number(), z.number()]),
  educationValues: z.record(z.string(), z.boolean()),
  genderValues: z.record(z.string(), z.boolean()),
  childrenValues: z.record(z.string(), z.boolean()),
  raceValues: z.record(z.string(), z.boolean()),
});

// Export types for TypeScript usage
export type AgeRange = z.infer<typeof AgeRangeSchema>;
export type IncomeRange = z.infer<typeof IncomeRangeSchema>;
export type EducationLevel = z.infer<typeof EducationLevelSchema>;
export type Gender = z.infer<typeof GenderSchema>;
export type RacialGroup = z.infer<typeof RacialGroupSchema>;
export type NumberOfChildren = z.infer<typeof NumberOfChildrenSchema>;
export type MaritalStatus = z.infer<typeof MaritalStatusSchema>;
export type HomeOwnership = z.infer<typeof HomeOwnershipSchema>;
export type HouseholdSize = z.infer<typeof HouseholdSizeSchema>;
export type LifestyleTraits = z.infer<typeof LifestyleTraitsSchema>;
export type PsychologicalTraits = z.infer<typeof PsychologicalTraitsSchema>;
export type PopulationTraits = z.infer<typeof PopulationTraitsSchema>;
export type PopulationTraitsValidationRequest = z.infer<
  typeof PopulationTraitsValidationRequestSchema
>;
export type FinalSelectedPopulationTraits = z.infer<
  typeof FinalSelectedPopulationTraitsSchema
>;
export type OneTraitChangeTraits = z.infer<typeof OneTraitChangeTraitsSchema>;
export type ValidationResponse = z.infer<typeof ValidationResponseSchema>;
export type StateData = z.infer<typeof StateDataSchema>;
export type DemographicTraitsForm = z.infer<typeof DemographicTraitsFormSchema>;
